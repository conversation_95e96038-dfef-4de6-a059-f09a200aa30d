import { zodResolver } from '@hookform/resolvers/zod';
import {
  ContractType,
  type CreditAccount,
  type CreditAccountOnboardingCampaignType,
  UsersignInMethod,
} from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppSearchParams,
  BANKLINK_PAYMENT_POLL_STATUSES,
  CampaignNamesByQueryParameter,
  CREDIT_LINE_PRICING_KEYS,
  FormFieldNames,
  GoogleAnalyticsEvents,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  PAYSERA_PAYMENT_STATUSES,
  PURCHASE_FLOW_LOG_ACTIONS,
  SigningPageViewTypes,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { coreApiUrl, onlyPasswordSigningEnabled } from 'environment';
import { useGetPageUrl } from 'hooks';
import { useGetBanklinkPaymentStatus } from 'hooks/use-banklink-payment-status-poll';
import { useBanklinkSigningPoll } from 'hooks/use-banklink-signing-poll';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspence';
import { useGetPricing } from 'hooks/use-get-pricing';
import { useLogCreditAccountAction } from 'hooks/use-log-credit-account-action';
import { useSignContractWithBanklink } from 'hooks/use-sign-contract-with-banklink';
import { useSignContractWithIdCard } from 'hooks/use-sign-contract-with-id-card';
import { useSignContractWithMobileId } from 'hooks/use-sign-contract-with-mobile-id';
import { useSignContractWithPassword } from 'hooks/use-sign-contract-with-password';
import { useSignContractWithSmartId } from 'hooks/use-sign-contract-with-smart-id';
import { useUpdateApplicationUserInfoSigning } from 'hooks/use-update-application-user-info-signing';
import { useUpdateCreditAccount } from 'hooks/use-update-credit-account';
import { useEffectOnce, useLocalStorage } from 'hooks/utils';
import type { CreditLineSigningPageLogic } from 'models';
import { useEffect, useMemo, useState } from 'react';
import { type FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  createSearchParams,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatPricingDataToObject,
  formatUserToSigningPageDataFormat,
  getFilteredUrlSearchParamsObject,
  getFromStorage,
  loadAndInitializeIdCardScripts,
  removeFromStorage,
  setToStorage,
} from 'services';
import { toast } from 'sonner';
import { processGqlFormValidationErrors } from 'utils';
import * as z from 'zod';

declare let iSignApplet: {
  init: (options: Record<string, unknown>) => void;
  getCertificate: (options: Record<string, unknown>) => void;
  setHashAlgorithm: (algorithm: string) => void;
  sign: (
    dtbs: string,
    dtbsHash: string,
    callback: (signedHash: string) => void,
  ) => void;
};

export type SigningFormType = z.infer<typeof SigningFormSchema>;

const SigningFormSchema = z.object({
  [FormFieldNames.iban]: z.string(),
});

export const useSigningPageLogic = (): CreditLineSigningPageLogic => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const payseraSigningPaymentStatus = searchParams.get(
    AppSearchParams.payseraSigningPaymentStatus,
  );
  const creditLineChosenAmount = getFromStorage(
    LocalStorageKeys.creditLineChosenAmount,
  );
  const { t: te, i18n } = useTranslation(LocizeNamespaces.errors);
  const { user, trackGoogleAnalyticsEvent, quietUserRefetch } =
    useRootContext();

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useGetPageUrl();
  const { logAction } = useLogCreditAccountAction();
  const { signContractWithPassword, signContractWithPasswordProcessing } =
    useSignContractWithPassword();
  const {
    signContractWithSmartId,
    prepareSmartIdContractSignature,
    smartIdContractSignaturePreparationChallenge,
    smartIdContractSignaturePreparationLoading,
    smartIdContractSignaturePreparationError,
  } = useSignContractWithSmartId();
  const { signContractWithIdCard, prepareIdCardContractSignature } =
    useSignContractWithIdCard();
  const {
    signContractWithMobileId,
    prepareMobileIdContractSignature,
    mobileIdContractSignaturePreparationChallenge,
    mobileIdContractSignaturePreparationLoading,
    mobileIdContractSignaturePreparationError,
  } = useSignContractWithMobileId();
  const {
    prepareBanklinkContractSignature,
    banklinkSigningProcessing,
    banklinkSigningAcceptUrl,
    banklinkSigningCancelUrl,
  } = useSignContractWithBanklink();
  const {
    banklinkPaymentStatus,
    startBanklinkPaymentStatusPolling,
    stopBanklinkPaymentStatusPolling,
  } = useGetBanklinkPaymentStatus();
  const {
    isBanklinkSigningPollSuccess,
    startBanklinkSigningStatusPolling,
    stopBanklinkSigningStatusPolling,
  } = useBanklinkSigningPoll();
  const { pageAttributes } = useGetPageAttributesSuspense();
  const { getBanklinks, banklinks } = useGetBanks();
  const { updateCreditAccount, creditAccountUpdating } =
    useUpdateCreditAccount();
  const { getPricingData, pricingData } = useGetPricing();
  const [storedQueryParams, storeQueryParams] = useLocalStorage<Record<
    string,
    string
  > | null>(LocalStorageKeys.storedQueryParams, null);

  const {
    updateApplicationUserInfoSigning,
    applicationUserInfoSigningUpdating,
    applicationUserInfoSigningUpdateError,
  } = useUpdateApplicationUserInfoSigning();

  const [signingPageViewType, setSigningPageViewType] = useState(
    SigningPageViewTypes.signing,
  );
  const [
    isPayseraSigningMethodDialogOpen,
    setIsPayseraSigningMethodDialogOpen,
  ] = useState(false);

  const [
    signContractWithIDCardProcessing,
    setSignContractWithIDCardProcessing,
  ] = useState(false);

  const pricingDataWithKeys = formatPricingDataToObject(pricingData);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const userInfoValidationErrors = extractValidationErrors(
    applicationUserInfoSigningUpdateError,
  );

  const isAllowedToChangeCreditLimit =
    visiblePageAttributes[PageAttributeNames.signingPageAmountSlider];

  const userCreditAccount = user?.credit_accounts?.length
    ? user.credit_accounts[0]
    : null;

  const userCreditAccountId = userCreditAccount?.id ?? 0;

  const signInMethod = user?.sign_in_method ?? null;

  const maxCreditAmount = userCreditAccount?.potential_credit_limit ?? 0;

  const isAvailableCreditAmount = creditLineChosenAmount <= maxCreditAmount;

  const userCanSignContract = signInMethod !== UsersignInMethod.PASSWORD;

  const contractLink = `${coreApiUrl}/credit-account/${userCreditAccountId}/generate-contract?contract-type=${ContractType.CREDIT_ACCOUNT_SIGNED}`;

  const isBanklinkSigningAllowed =
    !onlyPasswordSigningEnabled &&
    (signInMethod === UsersignInMethod.PAYSERA_BANKLINK ||
      signInMethod === UsersignInMethod.MAGIC_LINK);

  // FORM
  const { iban, applicationUserInfoId } = useMemo(
    () => formatUserToSigningPageDataFormat(user),
    [user],
  );

  const form = useForm<SigningFormType>({
    resolver: zodResolver(SigningFormSchema),
    defaultValues: {
      [FormFieldNames.iban]: iban,
    },
  });

  const onSigningFormSubmit = async ({ iban }: FieldValues) => {
    if (!applicationUserInfoId) {
      throw new Error('Application user info id is required');
    }

    try {
      const shouldIbanBeUpdated =
        form.formState.dirtyFields[FormFieldNames.iban] || !iban;

      if (shouldIbanBeUpdated) {
        const variablesFilteredByVisiblePageAttributes =
          filterObjectByExistingKeysInObject(
            {
              iban,
            },
            form.control._fields,
          );

        await updateApplicationUserInfoSigning({
          application_user_info_id: applicationUserInfoId,
          ...variablesFilteredByVisiblePageAttributes,
        });

        await quietUserRefetch();
      }

      await signContract();
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  const returnToSigningPage = () => {
    form.reset();
    setSigningPageViewType(SigningPageViewTypes.signing);
  };

  const updateCreditAccountOnboardingCampaign = () => {
    const onboardingParam = searchParams.get(AppSearchParams.onboarding) || '';
    const onboardingCampaign = CampaignNamesByQueryParameter[onboardingParam];

    if (onboardingCampaign) {
      return updateCreditAccount({
        credit_account_id: userCreditAccountId,
        onboarding_campaign:
          onboardingCampaign as CreditAccountOnboardingCampaignType,
      });
    }
  };

  const logSuccessfulSigningAction = () => {
    // LOGGING ACTION

    if (userCreditAccountId) {
      logAction({
        productId: userCreditAccountId,
        action: PURCHASE_FLOW_LOG_ACTIONS.successfullySigned,
      });
    }
  };

  const executeSuccessfulSigningCallbacks = () => {
    removeFromStorage(LocalStorageKeys.creditLineChosenAmount);
    trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contractSigned);
    logSuccessfulSigningAction();
    getPageUrlAndNavigate(true);
  };

  const signAppWithPassword = () => {
    setSigningPageViewType(SigningPageViewTypes.signingContract);
    signContractWithPassword({
      credit_account_id: userCreditAccountId,
      contract_type: ContractType.CREDIT_ACCOUNT_SIGNED,
    }).then(() => {
      executeSuccessfulSigningCallbacks();
    });
  };

  const prepareSigningAppWithSmartId = () => {
    setSigningPageViewType(SigningPageViewTypes.preparing);
    prepareSmartIdContractSignature({
      credit_account_id: userCreditAccountId,
      contract_type: ContractType.CREDIT_ACCOUNT_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signingFailed);
      });
  };

  const signAppWithIdCard = () => {
    const lang = user?.language_abbr.split('-')[0];

    iSignApplet.init({
      certificatePurpose: 'sign',
      codebase: `${window.location.protocol}//${window.location.host}/dokobit`,
      language: lang,
      supportedResidencies: ['ee'],
    });

    setSignContractWithIDCardProcessing(true);

    (
      window as unknown as Window & {
        certificateSelected: (certificate: string) => void;
      }
    ).certificateSelected = (certificate: string) => {
      const cert = btoa(unescape(encodeURIComponent(certificate)));
      prepareIdCardContractSignature({
        credit_account_id: userCreditAccountId,
        certificate: cert,
        contract_type: ContractType.CREDIT_ACCOUNT_SIGNED,
      })
        .then(({ data: prepareSignatureData }) => {
          iSignApplet.setHashAlgorithm(
            prepareSignatureData?.challenge?.algorithm || '',
          );

          iSignApplet.sign(
            prepareSignatureData?.challenge?.dtbs || '',
            prepareSignatureData?.challenge?.dtbs_hash || '',
            (signedHash: string) => {
              signContractWithIdCard({
                token: prepareSignatureData?.challenge?.token || '',
                signature: signedHash,
              })
                .then(({ data }) => {
                  if (data?.success) {
                    executeSuccessfulSigningCallbacks();
                  } else {
                    toast.error(
                      te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError),
                    );
                  }
                })
                .catch(() => {
                  setSignContractWithIDCardProcessing(false);
                });
            },
          );
        })
        .catch(() => {
          setSignContractWithIDCardProcessing(false);
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        });
    };
  };

  const prepareSigningAppWithMobileId = () => {
    setSigningPageViewType(SigningPageViewTypes.preparing);
    prepareMobileIdContractSignature({
      credit_account_id: userCreditAccountId,
      contract_type: ContractType.CREDIT_ACCOUNT_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signingFailed);
      });
  };

  const signAppWithMobileId = () =>
    signContractWithMobileId().then(({ data }) => {
      if (data?.success) {
        executeSuccessfulSigningCallbacks();
      } else {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    });

  const signAppWithSmartId = () =>
    signContractWithSmartId().then(({ data }) => {
      if (data?.success) {
        executeSuccessfulSigningCallbacks();
      } else {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    });

  const signAppByMobileIdOrSmartId = () => {
    setSigningPageViewType(SigningPageViewTypes.signingContract);
    if (signInMethod === UsersignInMethod.MOBILE) {
      return signAppWithMobileId();
    }
    // default is smart id
    return signAppWithSmartId();
  };

  const signAppWithBanklink = (payment_method_key: string) => {
    setSigningPageViewType(SigningPageViewTypes.preparing);

    prepareBanklinkContractSignature({
      credit_account_id: userCreditAccountId,
      contract_type: ContractType.CREDIT_ACCOUNT_SIGNED,
      payment_method_key,
      accept_url: banklinkSigningAcceptUrl,
      cancel_url: banklinkSigningCancelUrl,
    })
      .then(({ data }) => {
        const redirectUrl = data?.challenge?.redirect_url;
        const sessionId = data?.challenge?.session_id;

        const queryParamsToStore = getFilteredUrlSearchParamsObject(
          {
            [AppSearchParams.creditAccountHash]: true,
          },
          searchParams,
        );

        if (Object.keys(queryParamsToStore).length) {
          storeQueryParams(queryParamsToStore);
        }

        if (sessionId) {
          setToStorage(LocalStorageKeys.sessionId, sessionId);
        }

        if (redirectUrl) {
          window.location.href = redirectUrl;
        }
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signingFailed);
      });
  };

  const signContract = async () => {
    await updateCreditAccountOnboardingCampaign();

    if (onlyPasswordSigningEnabled) {
      signAppWithPassword();
      return;
    }

    switch (signInMethod) {
      case UsersignInMethod.PAYSERA_BANKLINK:
      case UsersignInMethod.MAGIC_LINK:
        if (isBanklinkSigningAllowed) {
          form.reset();
          setSigningPageViewType(SigningPageViewTypes.banklinkSigning);
        }
        break;
      case UsersignInMethod.MOBILE:
        prepareSigningAppWithMobileId();
        break;
      case UsersignInMethod.SMART_ID:
        prepareSigningAppWithSmartId();
        break;
      case UsersignInMethod.ID_CARD:
        signAppWithIdCard();
        break;
      default:
        signAppWithPassword();
        break;
    }
  };

  useEffectOnce(() => {
    if (storedQueryParams) {
      const params = Object.fromEntries(Array.from(searchParams));
      navigate(
        {
          search: createSearchParams({
            ...params,
            ...storedQueryParams,
          }).toString(),
        },
        { replace: true },
      );
      storeQueryParams(null);
    }
  });

  useEffectOnce(() => {
    const creditAccount = ((user?.credit_accounts ?? [])[0] ??
      {}) as CreditAccount;

    const creditLimitAmount =
      isAllowedToChangeCreditLimit && isAvailableCreditAmount
        ? creditLineChosenAmount ||
          creditAccount.credit_limit ||
          maxCreditAmount // this fix is for https://estoas.atlassian.net/browse/PPFV-529
        : maxCreditAmount;

    updateCreditAccount({
      credit_account_id: creditAccount.id,
      credit_limit: creditLimitAmount,
    });

    if (creditLimitAmount === 0) {
      throw new Error(
        `Credit limit amount is ${creditAccount.credit_limit}, Potential credit limit is ${creditAccount.potential_credit_limit}, Chosen amount is ${creditLineChosenAmount}, Is available credit amount is ${isAvailableCreditAmount}`,
      );
    }
  });

  useEffect(() => {
    if (isBanklinkSigningPollSuccess) {
      stopBanklinkSigningStatusPolling();
      executeSuccessfulSigningCallbacks();
    }
  }, [isBanklinkSigningPollSuccess]);

  useEffectOnce(() => {
    getPricingData({
      keys: Object.values(CREDIT_LINE_PRICING_KEYS),
    });
  });

  useEffect(() => {
    if (
      (visiblePageAttributes[PageAttributeNames.banklink] ||
        signInMethod === UsersignInMethod.MAGIC_LINK) &&
      !banklinks.length
    ) {
      getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
    }
  }, [pageAttributes?.length]);

  useEffectOnce(() => {
    if (payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setSigningPageViewType(SigningPageViewTypes.signingContract);
      startBanklinkPaymentStatusPolling().catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
    } else if (
      payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed
    ) {
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  });

  useEffect(() => {
    switch (banklinkPaymentStatus) {
      case BANKLINK_PAYMENT_POLL_STATUSES.success:
        stopBanklinkPaymentStatusPolling();
        startBanklinkSigningStatusPolling()
          .then(({ data }) => {
            if (data?.success) {
              stopBanklinkSigningStatusPolling();
              removeFromStorage(LocalStorageKeys.sessionId);
              executeSuccessfulSigningCallbacks();
            }
          })
          .catch((error) => {
            // Stop polling only if the code is not 3803
            // 3803 means that the Payment was not confirmed by Paysera
            if (error.code !== 3803) {
              stopBanklinkPaymentStatusPolling();
            }
          });
        break;
      case BANKLINK_PAYMENT_POLL_STATUSES.failed:
        stopBanklinkPaymentStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        setSigningPageViewType(SigningPageViewTypes.signing);
        break;
      default:
        break;
    }
  }, [banklinkPaymentStatus]);

  useEffect(() => {
    if (signInMethod === UsersignInMethod.ID_CARD) {
      loadAndInitializeIdCardScripts();
    }
  }, [signInMethod]);

  return useMemo(
    () => ({
      processingSigningPage:
        pageUrlAndNavigationProcessing ||
        signContractWithPasswordProcessing ||
        smartIdContractSignaturePreparationLoading ||
        signContractWithIDCardProcessing ||
        mobileIdContractSignaturePreparationLoading ||
        banklinkSigningProcessing ||
        applicationUserInfoSigningUpdating ||
        form.formState.isSubmitting,
      visiblePageAttributes,
      userCreditAccount,
      signAppWithBanklink,
      pricingDataWithKeys,
      contractLink,
      userCanSignContract,
      signInMethod,
      smartIdSigningChallengeViewIsVisible:
        Boolean(smartIdContractSignaturePreparationChallenge) &&
        !smartIdContractSignaturePreparationError,
      mobileIdSigningChallengeViewIsVisible:
        Boolean(mobileIdContractSignaturePreparationChallenge) &&
        !mobileIdContractSignaturePreparationError,
      smartIdContractSignaturePollChallengeId:
        smartIdContractSignaturePreparationChallenge?.challenge_id || '',
      mobileIdContractSignaturePollChallengeId:
        mobileIdContractSignaturePreparationChallenge?.challenge_id || '',
      banklinkOptions: banklinks,
      signingPageViewType,
      onPinConfirmationCancel: returnToSigningPage,
      maxCreditAmount,
      isAvailableCreditAmount,
      creditAccountUpdating,
      signAppByMobileIdOrSmartId,
      form,
      userInfoValidationErrors,
      onSigningFormSubmit,
      isPayseraSigningMethodDialogOpen,
      setIsPayseraSigningMethodDialogOpen,
      isBanklinkSigningAllowed,
      onTryAgainButtonClick: returnToSigningPage,
    }),
    [
      pageUrlAndNavigationProcessing,
      signContractWithPasswordProcessing,
      smartIdContractSignaturePreparationLoading,
      signContractWithIDCardProcessing,
      mobileIdContractSignaturePreparationLoading,
      banklinkSigningProcessing,
      visiblePageAttributes,
      userCreditAccount,
      signAppWithBanklink,
      pricingDataWithKeys,
      mobileIdContractSignaturePreparationChallenge,
      smartIdContractSignaturePreparationChallenge,
      smartIdContractSignaturePreparationError,
      mobileIdContractSignaturePreparationError,
      contractLink,
      userCanSignContract,
      banklinks,
      signingPageViewType,
      signInMethod,
      maxCreditAmount,
      isAvailableCreditAmount,
      creditAccountUpdating,
      signAppByMobileIdOrSmartId,
      form,
      userInfoValidationErrors,
      onSigningFormSubmit,
      isPayseraSigningMethodDialogOpen,
      applicationUserInfoSigningUpdating,
      isBanklinkSigningAllowed,
      returnToSigningPage,
    ],
  );
};
